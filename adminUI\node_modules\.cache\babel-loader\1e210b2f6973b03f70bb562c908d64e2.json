{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\product\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\product\\list.vue", "mtime": 1754364100857}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _brand = require(\"@/api/brand\");\nvar _auth = require(\"@/utils/auth\");\nvar _permission = require(\"@/utils/permission\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"BrandProductList\",\n  data: function data() {\n    return {\n      statusOptions: [{\n        value: -1,\n        label: this.$t(\"all\")\n      }, {\n        value: 1,\n        label: this.$t(\"online\")\n      }, {\n        value: 2,\n        label: this.$t(\"offline\")\n      }],\n      typeOptions: [{\n        value: \"1\",\n        label: this.$t(\"yes\")\n      }, {\n        value: \"0\",\n        label: this.$t(\"no\")\n      }],\n      loading: false,\n      listLoading: false,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      form: {\n        page: 1,\n        limit: 20,\n        keywords: \"\",\n        type: -1,\n        total: 0,\n        isIndex: false,\n        brand: \"\"\n      },\n      url: \"\",\n      brandName: \"\",\n      // dform: {\n      //   id: '',\n      //   url: '',\n      //   storeName: '',\n      //   image: '',\n      //   price: '',\n      //   cashbackRate: '',\n      //   // cashbackAmount: '',\n      //   status: '',\n      // },\n      dform: {},\n      status: \"\",\n      productDialogVisible: false,\n      multipleSelection: [],\n      brandOptions: []\n    };\n  },\n  mounted: function mounted() {\n    var brand = this.$route.query.brand || \"\";\n    this.form.brand = brand;\n    this.getList();\n    this.getBrands();\n  },\n  watch: {\n    \"$route.query.brand\": function $routeQueryBrand(newBrand) {\n      this.form.brand = newBrand;\n      this.getList();\n    }\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    formatAmount: function formatAmount(s) {\n      if (s == undefined) {\n        s = 0;\n      }\n      var s1 = (s / 1000).toFixed(3);\n      return s1;\n    },\n    getBrands: function getBrands() {\n      var _this2 = this;\n      (0, _brand.brandLstApi)({\n        page: 1,\n        limit: 10000,\n        type: \"-1\",\n        name: \"\"\n      }).then(function (res) {\n        if (res.list) {\n          var options = [];\n          for (var i in res.list) {\n            var itm = res.list[i];\n            options.push({\n              label: itm[\"name\"],\n              value: itm[\"code\"]\n            });\n          }\n          _this2.brandOptions = options;\n          console.log(_this2.brandOptions);\n        }\n        // this.tableData.data = res.list;\n        // this.tableData.total = res.total;\n        // this.listLoading = false;\n      }).catch(function (res) {\n        // this.listLoading = false;\n        // this.$message.error(this.$t(\"common.fetchDataFailed\"));\n      });\n    },\n    getList: function getList() {\n      var _this3 = this;\n      var _this = this;\n      this.listLoading = true;\n      if (this.form.type == -1) {\n        delete this.form.type;\n      }\n      (0, _brand.productListApi)(this.form).then(function (res) {\n        _this.listLoading = false;\n        _this.tableData.data = res.list;\n        _this.tableData.total = res.total;\n      }).catch(function (res) {\n        _this3.listLoading = false;\n        _this3.$message.error(_this3.$t(\"common.fetchDataFailed\"));\n      });\n    },\n    onSearch: function onSearch() {\n      this.form.page = 1;\n      this.getList();\n    },\n    onReset: function onReset() {\n      this.form.keywords = \"\";\n      this.form.status = \"-1\";\n    },\n    onAdd: function onAdd() {\n      this.productDialogVisible = true;\n    },\n    handleSelection: function handleSelection(val) {\n      this.multipleSelection = val;\n    },\n    batchHandle: function batchHandle(type) {\n      var _this = this;\n      var rows = [];\n      this.multipleSelection.forEach(function (row) {\n        rows.push(row.id);\n      });\n      if (rows.length > 0) {\n        this.listLoading = true;\n        var params = {\n          ids: rows\n        };\n        if (type === \"online\") {\n          (0, _brand.batchPutOn)(params).then(function (res) {\n            _this.getList();\n          });\n        } else if (type === \"outline\") {\n          (0, _brand.batchPutoff)(params).then(function (res) {\n            _this.getList();\n          });\n        } else if (type === \"delete\") {\n          params.type = \"recycle\";\n          (0, _brand.batchDelete)(params).then(function (res) {\n            _this.getList();\n          });\n        }\n      }\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.form.limit = val;\n      this.getList();\n    },\n    pageChange: function pageChange(page) {\n      this.form.page = page;\n      this.getList();\n    },\n    handleUpdate: function handleUpdate(row) {\n      var _this4 = this;\n      var _this = this;\n      var params = {\n        ids: [row.id]\n      };\n      if (!Boolean(row.isShow)) {\n        (0, _brand.batchPutoff)(params).then(function (res) {\n          //\n          // _this.getList();\n        }).catch(function (res) {\n          _this.$message.error(_this4.$t(\"common.operationFailed\"));\n        });\n      } else {\n        (0, _brand.batchPutOn)(params).then(function (res) {\n          // _this.getList();\n        }).catch(function (res) {\n          _this.$message.error(_this4.$t(\"common.operationFailed\"));\n        });\n      }\n    },\n    formatTime: function formatTime(t) {\n      var date = new Date(t * 1000);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, \"0\");\n      var day = String(date.getDate()).padStart(2, \"0\");\n      var hours = String(date.getHours()).padStart(2, \"0\");\n      var minutes = String(date.getMinutes()).padStart(2, \"0\");\n      var seconds = String(date.getSeconds()).padStart(2, \"0\");\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    },\n    editProduct: function editProduct(row) {\n      this.productDialogVisible = true;\n      this.dform = row;\n      this.status = row.isShow ? \"1\" : \"0\";\n      this.brandName = row.brand;\n      this.dform.forMatCashBackRate = this.formatRate(this.dform.cashBackRate);\n      // this.dform.id = row.id\n      // this.dform.storeName = row.storeName\n      // this.dform.image = row.image\n      // this.dform.price = row.price\n      // this.dform.cashbackRate = row.cashbackRate\n      // this.dform.cashbackAmount = row.cashbackAmount\n      // this.dform.status = row.status === '1' ? '1' : '0'\n      // this.productDialogVisible = true\n    },\n    isShowChange: function isShowChange(row, val, type) {\n      var _this5 = this;\n      var _this = this;\n      // let rows = [];\n\n      var item = {\n        id: row.id\n      };\n      if (val) {\n        item[type] = true;\n      } else {\n        item[type] = false;\n      }\n      // rows.push(item);\n      (0, _brand.updateProductInfo)(item).then(function (res) {\n        //_this.getList();\n      }).catch(function (res) {\n        _this5.$message.error(_this5.$t(\"common.operationFailed\"));\n      });\n    },\n    handleDelete: function handleDelete(row) {\n      var _this = this;\n      this.$confirm(this.$t(\"brand.confirmOperation\"), this.$t(\"brand.prompt\"), {\n        confirmButtonText: this.$t(\"brand.confirm\"),\n        cancelButtonText: this.$t(\"brand.cancel\"),\n        type: \"warning\",\n        showClose: false\n      }).then(function () {\n        var params = {\n          ids: [row.id],\n          type: \"recycle\"\n        };\n        (0, _brand.batchDelete)(params).then(function (res) {\n          _this.getList();\n        });\n      });\n    },\n    handleCloseProductDialog: function handleCloseProductDialog() {\n      this.productDialogVisible = false;\n    },\n    onSubProduct: function onSubProduct() {\n      var _this6 = this;\n      var _this = this;\n      if (this.dform && this.dform.id) {\n        (0, _brand.updateProductInfo)({\n          id: this.dform.id,\n          isShow: this.status == \"1\" ? true : false,\n          brand: this.brandName\n        }).then(function (res) {\n          _this.getList();\n          _this.productDialogVisible = false;\n        }).catch(function (res) {\n          _this.productDialogVisible = false;\n          _this6.$message.error(_this6.$t(\"common.operationFailed\"));\n        });\n      }\n    },\n    fetchProduct: function fetchProduct() {\n      var _this7 = this;\n      var _this = this;\n      (0, _brand.fetchProductApi)(6, this.url).then(function (res) {\n        _this.dform = res;\n        _this.dform.cashBackRate = _this.formatRate(_this.dform.cashBackRate);\n\n        // this.dform.storeName = res.storeName\n        // this.dform.image = res.image\n        // this.dform.price = res.price\n        // this.dform.cashbackRate = res.cashBackRate\n        // this.dform.cashbackAmount = res.cashBackAmount\n      }).catch(function (res) {\n        _this7.$message.error(_this7.$t(\"product.fetchProductFailed\"));\n      });\n    },\n    formatRate: function formatRate(s) {\n      return parseInt(s * 10000) / 100 + \"%\";\n    }\n  }\n};", null]}